{"format": 1, "restore": {"D:\\knaproject\\RentalChariot\\ModelTests\\ModelTests.csproj": {}}, "projects": {"D:\\knaproject\\RentalChariot\\ModelTests\\ModelTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\knaproject\\RentalChariot\\ModelTests\\ModelTests.csproj", "projectName": "ModelTests", "projectPath": "D:\\knaproject\\RentalChariot\\ModelTests\\ModelTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\knaproject\\RentalChariot\\ModelTests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\knaproject\\RentalChariot\\RentalChariot\\RentalChariot.csproj": {"projectPath": "D:\\knaproject\\RentalChariot\\RentalChariot\\RentalChariot.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[8.2.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\knaproject\\RentalChariot\\RentalChariot\\RentalChariot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\knaproject\\RentalChariot\\RentalChariot\\RentalChariot.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "D:\\knaproject\\RentalChariot\\RentalChariot\\RentalChariot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\knaproject\\RentalChariot\\RentalChariot\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}