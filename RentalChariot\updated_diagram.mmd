graph TB
    %% Client Layer
    Client[HTTP Client/API Consumer]
    
    %% API Layer
    subgraph "API Controllers"
        UserAuthController[UserAuthorizationController<br/>- Login/Logout<br/>- Create User<br/>- Ban/Unban Users]
        CarController[CarController<br/>- Create Car]
        RentController[RentController<br/>- Create Rent<br/>- Pay Rent]
        TestController[TestController<br/>- API Health Check<br/>- Test Endpoints]
    end
    
    %% Business Logic Layer
    subgraph "Domain Models & State Patterns"
        subgraph "User Domain"
            User[User<br/>- Name, Password<br/>- StateName]
            Admin[Admin : User<br/>- Ban/Unban capabilities]
            UserStates[User States<br/>- UnActiveState<br/>- ActiveState<br/>- BannedState]
            UserRoles[User Roles<br/>- UserRole<br/>- AdminRole]
        end
        
        subgraph "Car Domain"
            Car[Car<br/>- Brand, Model, Number<br/>- ProdYear, Color<br/>- EngineVol, Mileage<br/>- StateName]
            CarStates[Car States<br/>- AvaliableState<br/>- UnAvailableState<br/>- DeactivatedState]
        end
        
        subgraph "Rent Domain"
            Rent[Rent<br/>- UserId, CarId<br/>- RentDate, ReturnDate<br/>- StateName]
            RentStates[Rent States<br/>- UnPaidState<br/>- UnActiveState<br/>- ActiveState<br/>- EndedState<br/>- CancelledState<br/>- DeletedState]
        end
        
        LoginToken[LoginToken<br/>- UserId, Token<br/>- LoginTime]
    end
    
    %% Data Access Layer
    subgraph "Data Access Layer"
        UnitOfWork[UnitOfWork<br/>- Coordinates Repositories<br/>- Transaction Management]
        
        subgraph "Repositories"
            UserRepo[UserRepository<br/>- CRUD Operations<br/>- GetByName<br/>- GetUserByToken]
            CarRepo[CarRepository<br/>- CRUD Operations<br/>- IsExist]
            RentRepo[RentRepository<br/>- CreateRent<br/>- Update]
            LoginRepo[LoginRepository<br/>- Token Management<br/>- GetToken<br/>- GetTokenByUserId]
        end
    end
    
    %% Background Services
    subgraph "Background Processing"
        RentProcess[RentProcess<br/>- ProcessRentAsync<br/>- StartRent<br/>- EndRent<br/>- PayAsync]
    end
    
    %% Database Layer
    subgraph "Database"
        DbContext[RentalChariotDbContext<br/>Entity Framework Core]
        SqlServer[(SQL Server Database<br/>- Users Table<br/>- Cars Table<br/>- Rents Table<br/>- LoginTokens Table)]
    end
    
    %% DTOs
    subgraph "Data Transfer Objects"
        DTOs[DTOs<br/>- UserLoginRequest<br/>- UserCreateRequest<br/>- CarRequest<br/>- RentRequest<br/>- BanRequest<br/>- UnBanRequest<br/>- CurrentUser<br/>- ExternalUser]
    end
    
    %% Connections
    Client --> UserAuthController
    Client --> CarController
    Client --> RentController
    Client --> TestController
    
    UserAuthController --> DTOs
    CarController --> DTOs
    RentController --> DTOs
    
    UserAuthController --> UnitOfWork
    CarController --> UnitOfWork
    RentController --> UnitOfWork
    
    UnitOfWork --> UserRepo
    UnitOfWork --> CarRepo
    UnitOfWork --> RentRepo
    UnitOfWork --> LoginRepo
    
    UserRepo --> DbContext
    CarRepo --> DbContext
    RentRepo --> DbContext
    LoginRepo --> DbContext
    
    DbContext --> SqlServer
    
    User --> UserStates
    User --> UserRoles
    Admin --> UserRoles
    Car --> CarStates
    Rent --> RentStates
    
    RentController --> RentProcess
    RentProcess --> UnitOfWork
    
    %% Styling
    classDef controller fill:#e1f5fe
    classDef domain fill:#f3e5f5
    classDef data fill:#e8f5e8
    classDef background fill:#fff3e0
    classDef database fill:#fce4ec
    
    class UserAuthController,CarController,RentController,TestController controller
    class User,Admin,Car,Rent,LoginToken,UserStates,CarStates,RentStates,UserRoles domain
    class UnitOfWork,UserRepo,CarRepo,RentRepo,LoginRepo,DbContext data
    class RentProcess background
    class SqlServer database
