{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "Test1", "metadata": {"_parameterType": "resourceGroup", "description": "Name of the resource group for the resource. It is recommended to put resources under same resource group for better tracking."}}, "resourceGroupLocation": {"type": "string", "defaultValue": "polandcentral", "metadata": {"_parameterType": "location", "description": "Location of the resource group. Resource groups could have different location than resources."}}, "resourceLocation": {"type": "string", "defaultValue": "[parameters('resourceGroupLocation')]", "metadata": {"_parameterType": "location", "description": "Location of the resource. By default use resource group's location, unless the resource provider is not supported there."}}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "name": "[parameters('resourceGroupName')]", "location": "[parameters('resourceGroupLocation')]", "apiVersion": "2019-10-01"}, {"type": "Microsoft.Resources/deployments", "name": "[concat(parameters('resourceGroupName'), 'Deployment', uniqueString(concat('RentalChariot', subscription().subscriptionId)))]", "resourceGroup": "[parameters('resourceGroupName')]", "apiVersion": "2019-10-01", "dependsOn": ["[parameters('resourceGroupName')]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"name": "Mind-Your-Drink-<PERSON><PERSON><PERSON>", "type": "Microsoft.ApiManagement/service", "location": "[parameters('resourceLocation')]", "properties": {"publisherEmail": "<EMAIL>", "publisherName": "<PERSON>", "notificationSenderEmail": "<EMAIL>", "hostnameConfigurations": [{"type": "Proxy", "hostName": "mind-your-drink-serverapi.azure-api.net", "encodedCertificate": null, "keyVaultId": null, "certificatePassword": null, "negotiateClientCertificate": false, "certificate": null, "defaultSslBinding": true}], "publicIPAddresses": null, "privateIPAddresses": null, "additionalLocations": null, "virtualNetworkConfiguration": null, "customProperties": {"Microsoft.WindowsAzure.ApiManagement.Gateway.Security.Protocols.Tls10": "False", "Microsoft.WindowsAzure.ApiManagement.Gateway.Security.Protocols.Tls11": "False", "Microsoft.WindowsAzure.ApiManagement.Gateway.Security.Backend.Protocols.Tls10": "False", "Microsoft.WindowsAzure.ApiManagement.Gateway.Security.Backend.Protocols.Tls11": "False", "Microsoft.WindowsAzure.ApiManagement.Gateway.Security.Backend.Protocols.Ssl30": "False", "Microsoft.WindowsAzure.ApiManagement.Gateway.Protocols.Server.Http2": "False"}, "virtualNetworkType": "None", "certificates": null, "disableGateway": false, "apiVersionConstraint": {"minApiVersion": null}}, "sku": {"name": "Consumption", "capacity": 0}, "apiVersion": "2019-12-01"}, {"type": "Microsoft.ApiManagement/service/apis", "name": "Mind-Your-Drink-Serverap<PERSON>/Rental<PERSON>hariot", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apiRevision": "1", "description": null, "subscriptionRequired": true, "serviceUrl": null, "path": "RentalChariotURL", "protocols": ["https"], "authenticationSettings": {"oAuth2": null, "openid": null}, "subscriptionKeyParameterNames": {"header": "Ocp-Apim-Subscription-Key", "query": "subscription-key"}, "isCurrent": true}, "apiVersion": "2019-12-01", "dependsOn": ["Mind-Your-Drink-<PERSON><PERSON><PERSON>"]}]}}}], "metadata": {"_dependencyType": "apis.azure"}}