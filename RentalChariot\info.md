### System Architecture Overview

The **RentalChariot** system follows a **layered architecture** with **Domain-Driven Design (DDD)** principles and implements multiple **design patterns**:

**Architecture Layers:**
1. **API Layer**: RESTful controllers handling HTTP requests
2. **Domain Layer**: Business logic with rich domain models using State Pattern
3. **Data Access Layer**: Repository Pattern with Unit of Work for data persistence
4. **Background Processing**: Asynchronous rent processing services
5. **Database Layer**: Entity Framework Core with SQL Server

**Key Design Patterns:**
- **State Pattern**: Used extensively for User, Car, and Rent entities to manage their lifecycle states
- **Repository Pattern**: Abstracts data access logic
- **Unit of Work Pattern**: Manages transactions across multiple repositories
- **Factory Pattern**: Used for creating rent states (RentStateFactory)
- **DTO Pattern**: Separates API contracts from domain models

**Data Flow:**
1. HTTP requests hit controllers with DTOs
2. Controllers use Unit of Work to coordinate repository operations
3. Domain models apply business rules using state patterns
4. Background services handle time-based rent processing
5. Entity Framework persists changes to SQL Server

### Architecture Components

#### API Controllers
- **UserAuthorizationController**: Handles login/logout, user creation, ban/unban operations
- **CarController**: Manages car creation and fleet management
- **RentController**: Handles rental booking and payment processing

#### Domain Models with State Patterns
- **User Domain**: User/Admin entities with UnActive/Active/Banned states
- **Car Domain**: Car entity with Avaliable/UnAvaliable/Deactivated states
- **Rent Domain**: Rent entity with UnPaid/UnActive/Active/Ended/Cancelled/Deleted states
- **LoginToken**: Authentication token management with LoginTime tracking

#### Data Access Layer
- **UnitOfWork**: Coordinates repositories and manages transactions
- **Repositories**: UserRepository, CarRepository, RentRepository, LoginRepository
- **DbContext**: Entity Framework Core database context

#### Background Processing
- **RentProcess**: Handles asynchronous rent lifecycle management

## 2. Description of Programming Technologies Used

### **Core Framework & Runtime**
- **ASP.NET Core 9.0** - Modern web framework for building REST APIs
- **.NET 9.0** - Latest .NET runtime with improved performance and features
- **C#** - Primary programming language with modern language features

### **Database & ORM**
- **Microsoft SQL Server** - Relational database for data persistence
- **Entity Framework Core 9.0.5** - Object-Relational Mapping (ORM) framework
- **Entity Framework Tools** - Database migrations and scaffolding support

### **API & Documentation**
- **Microsoft.AspNetCore.OpenApi** - Built-in OpenAPI integration

### **Development & Tooling**
- **User Secrets** - Secure configuration management for development

### **Testing Framework**
- **xUnit** - Unit testing framework (from test project structure)
- **FluentAssertions** - Fluent assertion library for more readable tests

### **Design Patterns & Architecture**
- **Repository Pattern** - Data access abstraction
- **Unit of Work Pattern** - Transaction management
- **State Pattern** - Business logic state management
- **Dependency Injection** - Built-in ASP.NET Core DI container
- **Domain-Driven Design (DDD)** - Rich domain models with business logic

### **Security & Authentication**
- **Token-based Authentication** - Custom login token system (25-character random tokens)
- **Role-based Authorization** - Admin/User role separation

### **Background Processing**
- **Custom Background Services** - Asynchronous rent processing
- **Task-based Asynchronous Programming** - Modern async/await patterns

---

# 3. API Design

## User Authorization (`/UserAuthorization`)

### POST `/UserAuthorization/login`
**Purpose:** Authenticate a user and return a login token.

**Token Format:** System generates a random 25-character token using letters (A-Z, a-z) and numbers (0-9).

```json
Request: { "Name": "username", "Password": "password" }
Response: HTTP 200 OK { "loginToken": "Zej7OvgGodIAWK8OIfkkn1tqF", "message": "Login successful" }
Response: HTTP 401 Unauthorized "Invalid credentials"
```

**How Login Tokens Work:**
- **Generation**: System automatically creates a unique 25-character token when user logs in
- **Format**: Random combination of letters (A-Z, a-z) and numbers (0-9)
- **Example**: `"Zej7OvgGodIAWK8OIfkkn1tqF"` (this is just an example - each login generates a different token)
- **Usage**: Include this token in all API requests that require authentication
- **Security**: Token is stored in database and linked to specific user
- **Expiration**: Token is deleted when user logs out
### POST `/UserAuthorization/create`
**Purpose:** Register a new user account.

```json
Request: { "Name": "newuser", "Password": "securepassword" }
Response: HTTP 200 OK "User created successfully"
Response: HTTP 400 Bad Request "User already exists"
```

### POST `/UserAuthorization/logout`
**Purpose:** Invalidate a login token.

```json
Request: { "LoginToken": "Zej7OvgGodIAWK8OIfkkn1tqF" }
Response: HTTP 200 OK { "message": "Logout successful" }
Response: HTTP 401 Unauthorized "Invalid token"
```

### POST `/UserAuthorization/ban`
**Purpose:** Admin bans a user.

```json
Request: { "Admin": { "LoginToken": "Zej7OvgGodIAWK8OIfkkn1tqF" }, "UserToBan": { "Name": "target_user" } }
Response: HTTP 200 OK "Ban successful"
Response: HTTP 403 Forbidden "Admin access required"
Response: HTTP 404 Not Found "User not found"
```

### POST `/UserAuthorization/unban`
**Purpose:** Admin unbans a user.

```json
Request: { "Admin": { "LoginToken": "Zej7OvgGodIAWK8OIfkkn1tqF" }, "UserToUnBan": { "Name": "target_user" } }
Response: HTTP 200 OK "UnBan successful"
Response: HTTP 403 Forbidden "Admin access required"
Response: HTTP 404 Not Found "User not found"
```

## Car Management (`/Car`)

### POST `/Car/Create`
**Purpose:** Add a car to the rental fleet.

```json
Request: { "Brand": "Honda", "Model": "CRV", "Number": "RZ-7778", "ProdYear": "1997-02-01", "Color": "black", "EngineVol": "1800", "Mileage": "23000" }
Response: HTTP 200 OK "Car created"
Response: HTTP 400 Bad Request "Request is null"
Response: HTTP 401 Unauthorized "Car already exist with this number"
```

## Rental Management (`/Rent`)

### POST `/Rent/Create`
**Purpose:** Create a new rental.

```json
Request: { "User": { "LoginToken": "Zej7OvgGodIAWK8OIfkkn1tqF" }, "CarId": 1 }
Response: HTTP 200 OK "Rent created successfully"
Response: HTTP 400 Bad Request "Invalid request data"
Response: HTTP 401 Unauthorized "Invalid token"
Response: HTTP 404 Not Found "Car not found"
```

### POST `/Rent/Pay/{id}`
**Purpose:** Process payment for a rental.

```json
Response: HTTP 200 OK "Payment processed successfully"
Response: HTTP 404 Not Found "Rent not found"
Response: HTTP 400 Bad Request "Payment already processed"
```



## API Design Summary

* **RESTful endpoints**: Resource-oriented URIs and HTTP verbs.
* **Authentication**: All endpoints require a valid token.
* **Role-Based Access**: Ban/Unban restricted to admins.
* **State Transitions**: User, Car, and Rent objects follow lifecycle rules.
* **DTO-based contracts**: APIs consume well-structured DTOs, keeping models decoupled.

---
