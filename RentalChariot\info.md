# RentalChariot System Analysis

## 1. Implementation Diagram with Description

![System Architecture Diagram](diagram.png)

### System Architecture Overview

The **RentalChariot** system follows a **layered architecture** with **Domain-Driven Design (DDD)** principles and implements multiple **design patterns**:

**Architecture Layers:**
1. **API Layer**: RESTful controllers handling HTTP requests
2. **Domain Layer**: Business logic with rich domain models using State Pattern
3. **Data Access Layer**: Repository Pattern with Unit of Work for data persistence
4. **Background Processing**: Asynchronous rent processing services
5. **Database Layer**: Entity Framework Core with SQL Server

**Key Design Patterns:**
- **State Pattern**: Used extensively for User, Car, and Rent entities to manage their lifecycle states
- **Repository Pattern**: Abstracts data access logic
- **Unit of Work Pattern**: Manages transactions across multiple repositories
- **Factory Pattern**: Used for creating rent states (RentStateFactory)
- **DTO Pattern**: Separates API contracts from domain models

**Data Flow:**
1. HTTP requests hit controllers with DTOs
2. Controllers use Unit of Work to coordinate repository operations
3. Domain models apply business rules using state patterns
4. Background services handle time-based rent processing
5. Entity Framework persists changes to SQL Server

### Architecture Components

#### API Controllers
- **UserAuthorizationController**: Handles login/logout, user creation, ban/unban operations
- **CarController**: Manages car creation and fleet management
- **RentController**: Handles rental booking and payment processing

#### Domain Models with State Patterns
- **User Domain**: User/Admin entities with UnActive/Active/Banned states
- **Car Domain**: Car entity with Avaliable/UnAvaliable/Deactivated states (note: spelling as in code)
- **Rent Domain**: Rent entity with UnPaid/UnActive/Active/Ended/Cancelled/Deleted states
- **LoginToken**: Authentication token management

#### Data Access Layer
- **UnitOfWork**: Coordinates repositories and manages transactions
- **Repositories**: UserRepository, CarRepository, RentRepository, LoginRepository
- **DbContext**: Entity Framework Core database context

#### Background Processing
- **RentProcess**: Handles asynchronous rent lifecycle management

## 2. Description of Programming Technologies Used

### **Core Framework & Runtime**
- **ASP.NET Core 9.0** - Modern web framework for building REST APIs
- **.NET 9.0** - Latest .NET runtime with improved performance and features
- **C#** - Primary programming language with modern language features

### **Database & ORM**
- **Microsoft SQL Server** - Relational database for data persistence
- **Entity Framework Core 9.0.5** - Object-Relational Mapping (ORM) framework
- **Entity Framework Tools** - Database migrations and scaffolding support

### **API & Documentation**
- **ASP.NET Core OpenAPI 9.0.4** - OpenAPI/Swagger specification support for API documentation
- **Microsoft.AspNetCore.OpenApi** - Built-in OpenAPI integration

### **Development & Tooling**
- **Microsoft.VisualStudio.Web.CodeGeneration.Design 9.0.0** - Code scaffolding tools
- **User Secrets** - Secure configuration management for development

### **Testing Framework**
- **xUnit** - Unit testing framework (from test project structure)
- **FluentAssertions** - Fluent assertion library for more readable tests

### **Design Patterns & Architecture**
- **Repository Pattern** - Data access abstraction
- **Unit of Work Pattern** - Transaction management
- **State Pattern** - Business logic state management
- **Dependency Injection** - Built-in ASP.NET Core DI container
- **Domain-Driven Design (DDD)** - Rich domain models with business logic

### **Security & Authentication**
- **Token-based Authentication** - Custom login token system
- **Role-based Authorization** - Admin/User role separation

### **Background Processing**
- **Custom Background Services** - Asynchronous rent processing
- **Task-based Asynchronous Programming** - Modern async/await patterns

---

# 3. API Design

## User Authorization (`/UserAuthorization`)

### POST `/UserAuthorization/login`

**Purpose:** Authenticate a user and return a login token.

```json
Request:
{
  "Name": "username",
  "Password": "password"
}
```

```json
Response: HTTP 200 OK
{
  "loginToken": "Zej7OvgGodIAWK8OIfkkn1tqF",
  "message": "Login successful"
}
```

---

### POST `/UserAuthorization/create`

**Purpose:** Register a new user account.

```json
Request:
{
  "Name": "newuser",
  "Password": "securepassword"
}
```

```json
Response: HTTP 200 OK
"Account created successfully"
```

---

### POST `/UserAuthorization/logout`

**Purpose:** Invalidate a login token.

```json
Request:
{
  "LoginToken": "Zej7OvgGodIAWK8OIfkkn1tqF"
}
```

```json
Response: HTTP 200 OK
{
  "message": "Logout successful"
}
```

---

### POST `/UserAuthorization/ban`

**Purpose:** Admin bans a user.

```json
Request:
{
  "Admin": { "LoginToken": "admin_token" },
  "UserToBan": { "Name": "target_user" }
}
```

```json
Response: HTTP 200 OK
"Ban successful"
```

---

### POST `/UserAuthorization/unban`

**Purpose:** Admin unbans a user.

```json
Request:
{
  "Admin": { "LoginToken": "admin_token" },
  "UserToUnBan": { "Name": "target_user" }
}
```

```json
Response: HTTP 200 OK
"UnBan successful"
```

---

## Car Management (`/Car`)

### POST `/Car/Create`

**Purpose:** Add a car to the rental fleet.

```json
Request:
{
  "Brand": "Honda",
  "Model": "CRV",
  "Number": "RZ-7778",
  "ProdYear": "1997-02-01",
  "Color": "black",
  "EngineVol": "1800",
  "Mileage": "23000"
}
```

```json
Response: HTTP 200 OK
"Car created"
```

---

## Rental Management (`/Rent`)

### POST `/Rent/Create`

**Purpose:** Create a new rental.

```json
Request:
{
  "User": { "LoginToken": "user_token" },
  "CarId": 1
}
```

```json
Response: HTTP 200 OK
```

---

### POST `/Rent/Pay/{rentId}`

**Purpose:** Pay for a rental.

```json
Request: (rentId in URL path)
```

```json
Response: HTTP 200 OK
"Rent paid successfully"
```

---

## API Design Highlights

* **RESTful endpoints**: Resource-oriented URIs and HTTP verbs.
* **Authentication**: All endpoints require a valid token.
* **Role-Based Access**: Ban/Unban restricted to admins.
* **State Transitions**: User, Car, and Rent objects follow lifecycle rules.
* **DTO-based contracts**: APIs consume well-structured DTOs, keeping models decoupled.

---